{
  "recommendations": [
    // Essential Flutter & Dart Extensions
    "dart-code.dart-code",
    "dart-code.flutter",
    
    // Code Quality & Formatting
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-typescript-next",
    
    // Git & Version Control
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // Productivity Extensions
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "ms-vscode.vscode-json",
    
    // Debugging & Testing
    "ms-vscode.test-adapter-converter",
    
    // UI/UX Helpers
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "ms-vscode.vscode-json",
    
    // Additional Helpful Extensions
    "streetsidesoftware.code-spell-checker",
    "gruntfuggly.todo-tree",
    "ms-vscode.vscode-json",
    "usernamehw.errorlens",
    "christian-kohler.path-intellisense"
  ]
}
